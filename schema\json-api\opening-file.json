{"$schema": "http://json-schema.org/draft-07/schema", "$id": "https://example.com/example.json", "type": "object", "required": ["document", "editorConfig"], "properties": {"document": {"$id": "#/properties/document", "type": "object", "required": ["key", "permissions", "url"], "properties": {"key": {"$id": "#/properties/document/properties/key", "type": "string"}, "permissions": {"$id": "#/properties/document/properties/permissions", "type": "object", "required": [], "additionalProperties": true}, "url": {"$id": "#/properties/document/properties/url", "type": "string"}}, "additionalProperties": true}, "editorConfig": {"$id": "#/properties/editorConfig", "type": "object", "required": ["callbackUrl", "mode"], "properties": {"callbackUrl": {"$id": "#/properties/editorConfig/properties/callbackUrl", "type": "string"}, "mode": {"$id": "#/properties/editorConfig/properties/mode", "type": "string"}, "user": {"$id": "#/properties/editorConfig/properties/user", "type": "object", "required": [], "properties": {"group": {"$id": "#/properties/editorConfig/properties/user/properties/group", "type": "string"}, "id": {"$id": "#/properties/editorConfig/properties/user/properties/id", "anyOf": [{"$id": "#/properties/editorConfig/properties/user/properties/id/anyOf/0", "type": "string"}, {"$id": "#/properties/editorConfig/properties/user/properties/id/anyOf/1", "type": "integer"}]}, "name": {"$id": "#/properties/editorConfig/properties/user/properties/name", "type": "string"}}, "additionalProperties": true}}, "additionalProperties": true}}, "additionalProperties": true}