name: Oracle database tests
on:
  push:
    branches:
      - '**'
    paths:
      - 'tests/integration/databaseTests/**'
      - 'DocService/sources/databaseConnectors/baseConnector.js'
      - 'DocService/sources/databaseConnectors/oracleConnector.js'
jobs:
  oracle-tests:
    name: Oracle
    runs-on: ubuntu-latest

    steps:
      - name: Run Oracle DB docker container
        run: docker run --name oracle -p 8080:1521 -p 8081:5500 -e ORACLE_PASSWORD=admin -e APP_USER=onlyoffice -e APP_USER_PASSWORD=onlyoffice -d gvenzl/oracle-xe:21-slim

      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Caching dependencies
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: |
            ./npm-shrinkwrap.json
            ./Common/npm-shrinkwrap.json
            ./DocService/npm-shrinkwrap.json

      - name: Install modules
        run: |
          npm ci
          npm --prefix Common ci
          npm --prefix DocService ci

      - name: Creating service DB configuration
        run: |
          echo '{"services": {"CoAuthoring": {"sql": {"type": "oracle", "dbHost": "127.0.0.1", "dbPort": "8080", "dbUser": "onlyoffice", "dbPass": "onlyoffice", "dbName": "xepdb1"}}}}' >> Common/config/local.json

      - name: Await database service to finish startup
        run: sleep 15

      - name: Creating schema
        run: |
          docker cp ./schema/oracle/createdb.sql oracle:/
          docker exec oracle sqlplus -s onlyoffice/onlyoffice@//localhost/xepdb1 @/createdb.sql

      - name: Run Jest
        run: npm run "integration database tests"
