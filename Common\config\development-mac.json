{"log": {"filePath": "../Common/config/log4js/development.json"}, "storage": {"fs": {"folderPath": "../App_Data"}}, "wopi": {"enable": true}, "services": {"CoAuthoring": {"server": {"port": 8000, "static_content": {"/fonts": {"path": "../../fonts"}, "/sdkjs": {"path": "../../sdkjs"}, "/web-apps": {"path": "../../web-apps"}, "/sdkjs-plugins": {"path": "../../sdkjs-plugins"}, "/dictionaries": {"path": "../../dictionaries"}, "/welcome": {"path": "../branding/welcome"}, "/info": {"path": "../branding/info"}}}, "utils": {"utils_common_fontdir": "/Library/Fonts"}, "sql": {"type": "mysql", "dbPort": 3306, "dbUser": "root", "dbPass": "onlyoffice"}, "request-filtering-agent": {"allowPrivateIPAddress": true, "allowMetaIPAddress": true}, "sockjs": {"sockjs_url": "/web-apps/vendor/sockjs/sockjs.min.js"}, "socketio": {"connection": {"pingTimeout": 86400000, "pingInterval": 86400000}}}}, "license": {"license_file": "./../license.lic", "warning_limit_percents": 70, "packageType": 0}, "FileConverter": {"converter": {"fontDir": "", "presentationThemesDir": "../../sdkjs/slide/themes", "x2tPath": "../FileConverter/bin/x2t", "docbuilderPath": "../FileConverter/Bin/docbuilder", "errorfiles": "error"}}, "SpellChecker": {"server": {"dictDir": "../../dictionaries"}}}