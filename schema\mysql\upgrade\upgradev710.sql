DE<PERSON><PERSON><PERSON><PERSON> DLM00

DROP PROCEDURE IF EXISTS upgrade710 DLM00

CREATE PROCEDURE upgrade710()
BEGIN
	
	IF NOT EXISTS(SELECT * FROM information_schema.`COLUMNS` WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'task_result' AND COLUMN_NAME = 'additional') THEN
		ALTER TABLE `task_result` ADD COLUMN `additional` LONGTEXT NULL DEFAULT NULL AFTER `password`;
	END IF;

END DLM00

CALL upgrade710() DLM00

DELIMITER ;



