<PERSON><PERSON><PERSON><PERSON><PERSON> DLM00

DROP PROCEDURE IF EXISTS upgrade630 DLM00

CREATE PROCEDURE upgrade630()
BEGIN

	IF (SELECT DATA_TYPE FROM information_schema.`COLUMNS` WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'task_result' AND COLUMN_NAME = 'callback') <> 'longtext' THEN
		ALTER TABLE `task_result` CHANGE COLUMN `callback` `callback` LONGTEXT NOT NULL ;
	END IF;
	
	IF NOT EXISTS(SELECT * FROM information_schema.`COLUMNS` WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'task_result' AND COLUMN_NAME = 'created_at') THEN
		ALTER TABLE `task_result` ADD COLUMN `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `status_info`;
		ALTER TABLE `task_result` ADD COLUMN `password` LONGTEXT NULL AFTER `baseurl`;
	END IF;

END DLM00

CALL upgrade630() DLM00

DELIMITER ;
