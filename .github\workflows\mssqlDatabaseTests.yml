name: MSSQL database tests
on:
  push:
    branches:
      - '**'
    paths:
      - 'tests/integration/databaseTests/**'
      - 'DocService/sources/databaseConnectors/baseConnector.js'
      - 'DocService/sources/databaseConnectors/mssqlConnector.js'
jobs:
  mssql-tests:
    name: MSSQL
    runs-on: ubuntu-latest

    steps:
      - name: Run MSSQL DB docker container
        run: docker run --name mssql -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=onlYoff1ce" -p 8080:1433 -d mcr.microsoft.com/mssql/server:2022-latest

      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Caching dependencies
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: |
            ./npm-shrinkwrap.json
            ./Common/npm-shrinkwrap.json
            ./DocService/npm-shrinkwrap.json

      - name: Install modules
        run: |
          npm ci
          npm --prefix Common ci
          npm --prefix DocService ci

      - name: Creating service DB configuration
        run: |
          echo '{"services": {"CoAuthoring": {"sql": {"type": "mssql", "dbHost": "localhost", "dbPort": 8080, "dbUser": "sa", "dbPass": "onlYoff1ce"}}}}' >> Common/config/local.json

      - name: Await database service to finish startup
        run: sleep 5

      - name: Creating schema
        run: |
          docker cp ./schema/mssql/createdb.sql mssql:/
          docker exec mssql /opt/mssql-tools18/bin/sqlcmd -C -S localhost -U sa -P onlYoff1ce -Q "CREATE DATABASE onlyoffice;"
          docker exec mssql /opt/mssql-tools18/bin/sqlcmd -C -S localhost -U sa -P onlYoff1ce -d onlyoffice -i /createdb.sql

      - name: Run Jest
        run: npm run "integration database tests"
