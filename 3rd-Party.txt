﻿ONLYOFFICE DocumentServer uses code from the following 3rd party projects.

1. <PERSON>tra<PERSON> - Bootstrap is a free collection of tools for creating websites and web applications. It contains HTML and CSS-based design templates for typography, forms, buttons, navigation and other interface components, as well as optional JavaScript extensions.

URL:             http://getbootstrap.com
License:         Apache License Version 2.0
License File:    license/Bootstrap.license 


2. RequireJS - RequireJS is a JavaScript file and module loader. It is optimized for in-browser use, but it can be used in other JavaScript environments, like Rhino and Node. Using a modular script loader like RequireJS will improve the speed and quality of your code.

URL:            http://requirejs.org/
License:        The "New" BSD License, MIT License
License File:   license/RequireJS.license


3. jQuery - jQuery is a fast, small, and feature-rich JavaScript library.

URL:            https://jquery.org
License:        MIT License
License File:   license/jQuery.license


4. Megapixel - MFixes iOS6 Safari's image file rendering issue for large size image (over mega-pixel), which causes unexpected subsampling when drawing it in canvas.

URL:            https://github.com/stomita/ios-imagefile-megapixel
License:        MIT License
License File:   license/Megapixel.license


5. SocketIO - WebSocket emulation - Javascript client

URL:            https://socket.io
License:        MIT License
License File:   license/SocketIO.license


6.  Underscore - Underscore is a utility-belt library for JavaScript that provides a lot of the functional programming support that you would expect in Prototype.js (or Ruby), but without extending any of the built-in JavaScript objects. It's the tie to go along with jQuery's tux, and Backbone.js's suspenders.

URL:            https://github.com/jashkenas/underscore/
License:        MIT License
License File:   license/Underscore.license


7. XRegExp - XRegExp is an open source (MIT License) JavaScript library that provides augmented and extensible regular expressions. You get new syntax, flags, and methods beyond what browsers support natively. XRegExp is also a regex utility belt with tools to make your client-side grepping simpler and more powerful, while freeing you from worrying about pesky cross-browser inconsistencies and the dubious lastIndex property.

URL:            http://xregexp.com/
License:        MIT License
License File:   license/XRegExp.license


8. ZeroClipboard - The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface. 

URL:            http://zeroclipboard.org
License:        MIT License
License File:   license/ZeroClipboard.license


9. Hunspell - Hunspell is the spell checker of LibreOffice, OpenOffice.org, Mozilla Firefox 3 & Thunderbird, Google Chrome, and it is also used by proprietary software packages, like Mac OS X, InDesign, memoQ, Opera and SDL Trados.

URL:            http://sourceforge.net/projects/hunspell/
License:        MPL 1.1/GPL 2.0/LGPL 2.1
License File:   license/Hunspell.license


10. NodeHun - The Hunspell binding for nodejs that exposes as much of hunspell as possible and also adds new features.

URL:            https://npmjs.org/package/nodehun
License:        MIT License
License File:   license/NodeHun.license


11. Backbone - Backbone.js gives structure to web applications by providing models with key-value binding and custom events, collections with a rich API of enumerable functions, views with declarative event handling, and connects it all to your existing API over a RESTful JSON interface.

URL:            http://backbonejs.org/
License:        MIT License
License File:   license/Backbone.license


12. jQuery.browser - A jQuery plugin for browser detection.

URL:            http://api.jquery.com/jquery.browser/
License:        MIT License
License File:   license/jQuery.browser.license


13. PerfectScrollbar - Tiny but perfect jQuery scrollbar plugin.

URL:            http://noraesae.github.com/perfect-scrollbar/
License:        MIT License
License File:   license/PerfectScrollbar.license


14. jsrsasign - The 'jsrsasign' (RSA-Sign JavaScript Library) is a open source free pure JavaScript implementation of PKCS#1 v2.1 RSASSA-PKCS1-v1_5 RSA signing and validation algorithm.

URL:            http://kjur.github.io/jsrsasign/
License:        MIT License
License File:   license/jsrsasign.license


15. less - Less is a CSS pre-processor

URL:            http://lesscss.org/
License:        Apache 2 License
License File:   license/less.license


16. requirejs-text - A RequireJS/AMD loader plugin for loading text resources

URL:            http://lesscss.org/
License:        MIT License
License File:   license/requirejs-text.license