{"log": {"filePath": "../../config/log4js/production.json"}, "storage": {"fs": {"folderPath": "../App_Data/cache/files"}}, "services": {"CoAuthoring": {"server": {"static_content": {"/fonts": {"path": "../../fonts", "options": {"maxAge": "7d"}}, "/sdkjs": {"path": "../../sdkjs", "options": {"maxAge": "7d"}}, "/web-apps": {"path": "../../web-apps", "options": {"maxAge": "7d"}}, "/sdkjs-plugins": {"path": "../../sdkjs-plugins", "options": {"maxAge": "7d"}}, "/dictionaries": {"path": "../../dictionaries", "options": {"maxAge": "7d"}}, "/welcome": {"path": "../welcome", "options": {"maxAge": "7d"}}, "/info": {"path": "../info", "options": {"maxAge": "7d"}}}}, "utils": {"utils_common_fontdir": "C:\\Windows\\Fonts"}, "sockjs": {"sockjs_url": "/web-apps/vendor/sockjs/sockjs.min.js"}}}, "license": {"license_file": "./../license.lic", "warning_limit_percents": 70, "packageType": 0}, "FileConverter": {"converter": {"fontDir": "", "presentationThemesDir": "../../sdkjs/slide/themes", "x2tPath": "../FileConverter/bin/x2t.exe", "docbuilderPath": "../FileConverter/bin/docbuilder.exe"}}, "SpellChecker": {"server": {"dictDir": "../../dictionaries"}}}