name: Postgre database tests
on:
  push:
    branches:
      - '**'
    paths:
      - 'tests/integration/databaseTests/**'
      - 'DocService/sources/databaseConnectors/baseConnector.js'
      - 'DocService/sources/databaseConnectors/postgreConnector.js'
jobs:
  postgres-tests:
    name: Postgres
    runs-on: ubuntu-latest

    steps:
      - name: Run Postgres DB docker container
        run: docker run --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=onlyoffice -e POSTGRES_USER=onlyoffice -e POSTGRES_DB=onlyoffice -d postgres:latest

      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Caching dependencies
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: |
            ./npm-shrinkwrap.json
            ./Common/npm-shrinkwrap.json
            ./DocService/npm-shrinkwrap.json

      - name: Install modules
        run: |
          npm ci
          npm --prefix Common ci
          npm --prefix DocService ci

      - name: Creating service DB configuration
        run: |
          echo '{"services": {"CoAuthoring": {"sql": {"dbHost": "127.0.0.1"}}}}' >> Common/config/local.json

      - name: Creating schema
        run: |
          docker cp ./schema/postgresql/createdb.sql postgres:/
          docker exec postgres psql -d onlyoffice -U onlyoffice -a -f /createdb.sql

      - name: Run Jest
        run: npm run "integration database tests"
