name: Dameng database tests
on:
  push:
    branches:
      - '**'
    paths:
      - 'tests/integration/databaseTests/**'
      - 'DocService/sources/databaseConnectors/baseConnector.js'
      - 'DocService/sources/databaseConnectors/damengConnector.js'
jobs:
  dameng-tests:
    name: Dame<PERSON>
    runs-on: ubuntu-latest

    steps:
      - name: Run dameng DB docker container
        run: docker run --name dameng -p 5236:5236 -e PAGE_SIZE=16 -e LD_LIBRARY_PATH=/opt/dmdbms/bin -e INSTANCE_NAME=dm8_01 -d danilaworker/damengdb:8.1.2

      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Caching dependencies
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: |
            ./npm-shrinkwrap.json
            ./Common/npm-shrinkwrap.json
            ./DocService/npm-shrinkwrap.json

      - name: Install modules
        run: |
          npm ci
          npm --prefix Common ci
          npm --prefix DocService ci

      - name: Await database service to finish startup
        run: sleep 15

      - name: Creating service DB configuration
        run: |
          echo '{"services": {"CoAuthoring": {"sql": {"type": "dameng", "dbHost": "127.0.0.1", "dbPort": 5236, "dbUser": "SYSDBA", "dbPass": "SYSDBA001"}}}}' >> Common/config/local.json

      - name: Creating schema
        run: |
          docker cp ./schema/dameng/createdb.sql dameng:/
          docker exec dameng bash -c "cat /createdb.sql | /opt/dmdbms/bin/disql SYSDBA/SYSDBA001:5236"

      - name: Run Jest
        run: npm run "integration database tests"
