{"name": "builder", "version": "1.0.1", "homepage": "https://www.onlyoffice.com", "private": true, "grunt": {"copy": {"server": {"expand": true, "src": ["./**/sources/*.js", "./Common/package.json", "./DocService/package.json", "./DocService/public/healthcheck.docx", "./FileConverter/package.json", "./FileConverter/bin/DoctRenderer.config", "./Metrics/package.json", "./Common/config/*.json", "./Common/config/log4js/*.json", "./Metrics/config/config.js"], "dest": "./build/server"}}, "develop-copy": {"server": {}}, "clean": {"options": {"force": true}, "server": "./build/server"}, "mkdir": {"server": {"options": {"create": ["./build/server"]}}}}, "postprocess": {"src": ["./build/server/**/sources/*.js"], "dest": "./"}, "npm": ["./build/server/Common", "./build/server/DocService", "./build/server/FileConverter", "./build/server/Metrics"], "dependencies": {"builder": "file:", "grunt": "1.5.3", "grunt-banner": "0.6.0", "grunt-check-dependencies": "1.0.0", "grunt-contrib-clean": "2.0.0", "grunt-contrib-copy": "1.0.0", "grunt-mkdir": "1.1.0", "grunt-stripcomments": "0.7.2", "license-downloader": "1.0.8", "license-report": "6.5.0", "npm-run-all": "4.1.5"}, "devDependencies": {"@jest/globals": "29.5.0", "cross-env": "7.0.3", "jest": "29.5.0"}, "scripts": {"perf-expired": "cd ./DocService&& cross-env NODE_ENV=development-windows NODE_CONFIG_DIR=../Common/config node ../tests/perf/checkFileExpire.js", "perf-exif": "cd ./DocService&& cross-env NODE_ENV=development-windows NODE_CONFIG_DIR=../Common/config node ../tests/perf/fixImageExifRotation.js", "perf-png": "cd ./DocService&& cross-env NODE_ENV=development-windows NODE_CONFIG_DIR=../Common/config node ../tests/perf/convertImageToPng.js", "unit tests": "cd ./DocService && jest unit --inject-globals=false --config=../tests/jest.config.js", "integration tests with server instance": "cd ./DocService && jest integration/withServerInstance --inject-globals=false --config=../tests/jest.config.js", "integration database tests": "cd ./DocService && jest integration/databaseTests --inject-globals=false --config=../tests/jest.config.js", "tests": "cd ./DocService && jest --inject-globals=false --config=../tests/jest.config.js", "install:Common": "npm ci --prefix ./Common", "install:DocService": "npm ci --prefix ./DocService", "install:FileConverter": "npm ci --prefix ./FileConverter", "install:Metrics": "npm ci --prefix ./Metrics", "3d-party-lic-json:Common": "license-report --output=json --package=./Common/package.json --config ./3d-party-lic-report/license-report-config.json > ./3d-party-lic-report/license-report.json", "3d-party-lic-json:DocService": "license-report --output=json --package=./DocService/package.json --config ./3d-party-lic-report/license-report-config.json > ./3d-party-lic-report/license-report.json", "3d-party-lic-json:FileConverter": "license-report --output=json --package=./FileConverter/package.json --config ./3d-party-lic-report/license-report-config.json > ./3d-party-lic-report/license-report.json", "3d-party-lic-json:Metrics": "license-report --output=json --package=./Metrics/package.json --config ./3d-party-lic-report/license-report-config.json > ./3d-party-lic-report/license-report.json", "3d-party-lic-downloader": "license-downloader --source ./3d-party-lic-report/license-report.json", "3d-party-lic-md-header": "node ./3d-party-lic-report/json2md.js ./3DPARTY.md", "3d-party-lic-md": "node ./3d-party-lic-report/json2md.js ./3DPARTY.md ./3d-party-lic-report/license-report.ext.json", "3d-party-lic-report:Common": "run-s 3d-party-lic-json:Common 3d-party-lic-downloader 3d-party-lic-md", "3d-party-lic-report:DocService": "run-s 3d-party-lic-json:DocService 3d-party-lic-downloader 3d-party-lic-md", "3d-party-lic-report:FileConverter": "run-s 3d-party-lic-json:FileConverter 3d-party-lic-downloader 3d-party-lic-md", "3d-party-lic-report:Metrics": "run-s 3d-party-lic-json:Metrics 3d-party-lic-downloader 3d-party-lic-md", "3d-party-lic-report": "run-s 3d-party-lic-md-header 3d-party-lic-report:*", "build": "run-p install:*"}}