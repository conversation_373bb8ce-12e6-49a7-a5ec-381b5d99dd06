name: MYSQL database tests
on:
  push:
    branches:
      - '**'
    paths:
      - 'tests/integration/databaseTests/**'
      - 'DocService/sources/databaseConnectors/baseConnector.js'
      - 'DocService/sources/databaseConnectors/mysqlConnector.js'
jobs:
  mysql-tests:
    name: MYSQL
    runs-on: ubuntu-latest

    steps:
      - name: Run Mysql DB docker container
        run: docker run --name mysql -p 8080:3306 -p 8081:33060 -e MYSQL_HOST=127.0.0.1 -e MYSQL_ROOT_PASSWORD=onlyoffice -e MYSQL_DATABASE=onlyoffice -d mysql:latest

      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Caching dependencies
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: |
            ./npm-shrinkwrap.json
            ./Common/npm-shrinkwrap.json
            ./DocService/npm-shrinkwrap.json

      - name: Install modules
        run: |
          npm ci
          npm --prefix Common ci
          npm --prefix DocService ci

      - name: Creating service DB configuration
        run: |
          echo '{"services": {"CoAuthoring": {"sql": {"type": "mysql", "dbHost": "127.0.0.1", "dbPort": "8080", "dbUser": "root", "dbPass": "onlyoffice"}}}}' >> Common/config/local.json

      - name : Creating schema
        run: |
          docker cp ./schema/mysql/createdb.sql mysql:/
          docker exec mysql mysql -h 127.0.0.1 -u root --password=onlyoffice -D onlyoffice -e 'source /createdb.sql'

      - name: Run Jest
        run: npm run "integration database tests"
