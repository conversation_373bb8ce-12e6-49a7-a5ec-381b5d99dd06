
## Third-party

- @aws-sdk/client-s3 3.637.0 ([Apache-2.0](https://raw.githubusercontent.com/aws/aws-sdk-js-v3/main/LICENSE))
- @aws-sdk/node-http-handler 3.374.0 ([Apache-2.0](https://raw.githubusercontent.com/aws/aws-sdk-js-v3/main/LICENSE))
- @aws-sdk/s3-request-presigner 3.370.0 ([Apache-2.0](https://raw.githubusercontent.com/aws/aws-sdk-js-v3/main/LICENSE))
- amqplib 0.8.0 ([MIT](https://raw.githubusercontent.com/amqp-node/amqplib/main/LICENSE))
- co 4.6.0 ([MIT](https://raw.githubusercontent.com/tj/co/master/LICENSE))
- config 2.0.1 ([MIT](https://raw.githubusercontent.com/node-config/node-config/master/LICENSE))
- content-disposition 0.5.3 ([MIT](https://raw.githubusercontent.com/jshttp/content-disposition/master/LICENSE))
- dnscache 1.0.1 ([BSD](https://raw.githubusercontent.com/yahoo/dnscache/master/LICENSE))
- escape-string-regexp 1.0.5 ([MIT](https://raw.githubusercontent.com/sindresorhus/escape-string-regexp/main/license))
- forwarded 0.1.2 ([MIT](https://raw.githubusercontent.com/jshttp/forwarded/master/LICENSE))
- ipaddr.js 1.8.1 ([MIT](https://raw.githubusercontent.com/whitequark/ipaddr.js/main/LICENSE))
- jsonwebtoken 9.0.0 ([MIT](https://raw.githubusercontent.com/auth0/node-jsonwebtoken/master/LICENSE))
- log4js 6.4.1 ([Apache-2.0](https://raw.githubusercontent.com/log4js-node/log4js-node/master/LICENSE))
- mime 2.3.1 ([MIT](https://raw.githubusercontent.com/broofa/mime/main/LICENSE))
- ms 2.1.1 ([MIT](https://raw.githubusercontent.com/vercel/ms/main/license.md))
- node-cache 4.2.1 ([MIT](https://raw.githubusercontent.com/node-cache/node-cache/master/LICENSE))
- node-statsd 0.1.1 ([MIT](https://raw.githubusercontent.com/sivy/node-statsd/master/LICENSE))
- nodemailer 6.9.13 ([MIT-0](https://raw.githubusercontent.com/nodemailer/nodemailer/master/LICENSE))
- request 2.88.0 ([Apache-2.0](https://raw.githubusercontent.com/request/request/master/LICENSE))
- request-filtering-agent 1.0.5 ([MIT](https://raw.githubusercontent.com/azu/request-filtering-agent/master/LICENSE))
- rhea 1.0.24 ([Apache-2.0](https://raw.githubusercontent.com/amqp/rhea/main/LICENSE))
- uri-js 4.2.2 ([BSD-2-Clause](https://raw.githubusercontent.com/garycourt/uri-js/master/LICENSE))
- win-ca 3.5.0 ([MIT](https://raw.githubusercontent.com/ukoloff/win-ca/master/LICENSE))
- ajv 8.9.0 ([MIT](https://raw.githubusercontent.com/ajv-validator/ajv/master/LICENSE))
- apicache 1.6.3 ([MIT](https://raw.githubusercontent.com/kwhitley/apicache/master/LICENSE))
- base64-stream 1.0.0 ([MIT](https://github.com/mazira/base64-stream?tab=readme-ov-file#license))
- body-parser 1.20.1 ([MIT](https://raw.githubusercontent.com/expressjs/body-parser/master/LICENSE))
- bottleneck 2.19.5 ([MIT](https://raw.githubusercontent.com/SGrondin/bottleneck/master/LICENSE))
- bytes 3.0.0 ([MIT](https://raw.githubusercontent.com/visionmedia/bytes.js/master/LICENSE))
- co 4.6.0 ([MIT](https://raw.githubusercontent.com/tj/co/master/LICENSE))
- config 2.0.1 ([MIT](https://raw.githubusercontent.com/node-config/node-config/master/LICENSE))
- cron 1.5.0 ([MIT](https://raw.githubusercontent.com/kelektiv/node-cron/main/LICENSE))
- deep-equal 1.0.1 ([MIT](https://raw.githubusercontent.com/inspect-js/node-deep-equal/main/LICENSE))
- dmdb 1.0.14280 ([none](https://www.npmjs.com/package/dmdb))
- ejs 3.1.10 ([Apache-2.0](https://raw.githubusercontent.com/mde/ejs/main/LICENSE))
- exif-parser 0.1.12 ([MIT](https://raw.githubusercontent.com/bwindels/exif-parser/master/LICENSE.md))
- express 4.19.2 ([MIT](https://raw.githubusercontent.com/expressjs/express/master/LICENSE))
- fakeredis 2.0.0 ([MIT](https://github.com/hdachev/fakeredis?tab=readme-ov-file#license))
- ioredis 5.3.1 ([MIT](https://raw.githubusercontent.com/redis/ioredis/main/LICENSE))
- jimp 0.22.10 ([MIT](https://raw.githubusercontent.com/jimp-dev/jimp/main/LICENSE))
- jsonwebtoken 9.0.0 ([MIT](https://raw.githubusercontent.com/auth0/node-jsonwebtoken/master/LICENSE))
- jwa 1.1.6 ([MIT](https://raw.githubusercontent.com/auth0/node-jwa/master/LICENSE))
- mime 2.3.1 ([MIT](https://raw.githubusercontent.com/broofa/mime/main/LICENSE))
- mime-db 1.49.0 ([MIT](https://raw.githubusercontent.com/jshttp/mime-db/master/LICENSE))
- ms 2.1.1 ([MIT](https://raw.githubusercontent.com/vercel/ms/master/license.md))
- mssql 9.1.1 ([MIT](https://raw.githubusercontent.com/tediousjs/node-mssql/master/LICENSE.md))
- multer 1.4.3 ([MIT](https://raw.githubusercontent.com/expressjs/multer/master/LICENSE))
- multi-integer-range 4.0.7 ([MIT](https://raw.githubusercontent.com/smikitky/node-multi-integer-range/master/LICENSE))
- multiparty 4.2.1 ([MIT](https://raw.githubusercontent.com/pillarjs/multiparty/master/LICENSE))
- mysql2 3.9.8 ([MIT](https://raw.githubusercontent.com/sidorares/node-mysql2/master/License))
- oracledb 6.3.0 ([(Apache-2.0 OR UPL-1.0)](https://raw.githubusercontent.com/oracle/node-oracledb/main/LICENSE.txt))
- pg 8.11.3 ([MIT](https://raw.githubusercontent.com/brianc/node-postgres/master/LICENSE))
- redis 4.6.11 ([MIT](https://raw.githubusercontent.com/redis/node-redis/master/LICENSE))
- retry 0.12.0 ([MIT](https://raw.githubusercontent.com/tim-kos/node-retry/master/License))
- socket.io 4.8.1 ([MIT](https://raw.githubusercontent.com/socketio/socket.io/main/LICENSE))
- underscore 1.13.1 ([MIT](https://raw.githubusercontent.com/jashkenas/underscore/master/LICENSE))
- utf7 1.0.2 ([BSD](https://www.npmjs.com/package/utf7))
- windows-locale 1.0.1 ([MIT](https://raw.githubusercontent.com/TiagoDanin/Windows-Locale/master/LICENSE))
- xmlbuilder2 3.0.2 ([MIT](https://raw.githubusercontent.com/oozcitak/xmlbuilder2/master/LICENSE))
- @expo/spawn-async 1.7.2 ([MIT](https://raw.githubusercontent.com/TritonDataCenter/node-spawn-async/master/LICENSE))
- bytes 3.0.0 ([MIT](https://raw.githubusercontent.com/visionmedia/bytes.js/master/LICENSE))
- co 4.6.0 ([MIT](https://raw.githubusercontent.com/tj/co/master/LICENSE))
- config 2.0.1 ([MIT](https://github.com/node-config/node-config/blob/master/LICENSE))
- lcid 3.1.1 ([MIT](https://raw.githubusercontent.com/sindresorhus/lcid/main/license))
- statsd 0.8.4 ([MIT](https://raw.githubusercontent.com/statsd/statsd/master/LICENSE))
